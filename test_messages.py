#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
站内信系统测试脚本
"""

import json
import os
from message_system import MessageSystem
from auth import UserManager

def test_message_system():
    """测试站内信系统"""
    print("=== 站内信系统测试 ===")
    
    # 创建测试实例
    message_system = MessageSystem('test_messages.json')
    user_manager = UserManager()
    
    # 测试发送消息给单个用户
    print("\n1. 测试发送消息给单个用户")
    success, result = message_system.send_message(
        sender='admin',
        recipient='testuser',
        title='欢迎使用站内信系统',
        content='这是一条测试消息，欢迎使用我们的站内信功能！',
        message_type='admin'
    )
    print(f"发送结果: {success}, {result}")
    
    # 测试发送消息给所有用户
    print("\n2. 测试发送消息给所有用户")
    success, result = message_system.send_message(
        sender='admin',
        recipient='all',
        title='系统公告',
        content='系统将在今晚进行维护，请大家提前保存工作。',
        message_type='system'
    )
    print(f"发送结果: {success}, {result}")
    
    # 测试获取用户消息
    print("\n3. 测试获取用户消息")
    messages = message_system.get_user_messages('testuser', limit=10)
    print(f"获取到 {len(messages)} 条消息")
    for msg in messages:
        print(f"  - {msg['title']} (发送者: {msg['sender']}, 类型: {msg['type']})")
    
    # 测试获取未读消息数量
    print("\n4. 测试获取未读消息数量")
    unread_count = message_system.get_unread_count('testuser')
    print(f"未读消息数量: {unread_count}")
    
    # 测试标记消息为已读
    if messages:
        print("\n5. 测试标记消息为已读")
        first_message = messages[0]
        success, msg = message_system.mark_as_read(first_message['id'], 'testuser')
        print(f"标记已读结果: {success}, {msg}")
        
        # 再次检查未读数量
        unread_count = message_system.get_unread_count('testuser')
        print(f"标记后未读消息数量: {unread_count}")
    
    # 测试获取统计信息
    print("\n6. 测试获取统计信息")
    stats = message_system.get_statistics()
    print(f"统计信息: {json.dumps(stats, indent=2, ensure_ascii=False)}")
    
    # 测试批量标记已读
    print("\n7. 测试批量标记已读")
    success, msg = message_system.mark_all_as_read('testuser')
    print(f"批量标记已读结果: {success}, {msg}")
    
    # 最终检查未读数量
    unread_count = message_system.get_unread_count('testuser')
    print(f"最终未读消息数量: {unread_count}")
    
    # 清理测试文件
    if os.path.exists('test_messages.json'):
        os.remove('test_messages.json')
    
    print("\n=== 测试完成 ===")

def test_api_endpoints():
    """测试API端点（需要应用程序运行）"""
    print("\n=== API端点测试 ===")
    
    import requests
    
    base_url = 'http://localhost:7799'
    
    # 测试获取未读消息数量（需要登录）
    try:
        response = requests.get(f'{base_url}/api/messages/unread_count')
        print(f"未读消息数量API: {response.status_code}")
        if response.status_code == 200:
            data = response.json()
            print(f"响应: {data}")
    except Exception as e:
        print(f"API测试失败: {e}")

if __name__ == "__main__":
    test_message_system()
    test_api_endpoints()
