#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
站内信系统
用于管理用户之间的站内消息
"""

import json
import os
import uuid
from typing import Dict, List, Optional, Tuple
from datetime import datetime
from beijing_time import beijing_now_iso, beijing_now, format_beijing_time


class MessageSystem:
    """站内信系统"""
    
    def __init__(self, data_file: str = 'messages.json'):
        self.data_file = data_file
        self.messages_data = self.load_messages_data()
    
    def load_messages_data(self) -> Dict:
        """加载消息数据"""
        if os.path.exists(self.data_file):
            try:
                with open(self.data_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except (json.JSONDecodeError, IOError) as e:
                print(f"加载消息数据失败: {e}")
                return self.create_default_data()
        else:
            return self.create_default_data()
    
    def create_default_data(self) -> Dict:
        """创建默认数据结构"""
        return {
            'messages': [],  # 消息记录
            'statistics': {
                'total_messages': 0,
                'total_read_messages': 0,
                'total_unread_messages': 0,
                'total_system_messages': 0,
                'total_admin_messages': 0
            }
        }
    
    def save_messages_data(self) -> bool:
        """保存消息数据"""
        try:
            # 创建备份
            if os.path.exists(self.data_file):
                backup_file = f"{self.data_file}.backup"
                with open(self.data_file, 'r', encoding='utf-8') as src:
                    with open(backup_file, 'w', encoding='utf-8') as dst:
                        dst.write(src.read())
            
            # 保存新数据
            with open(self.data_file, 'w', encoding='utf-8') as f:
                json.dump(self.messages_data, f, ensure_ascii=False, indent=2)
            return True
        except IOError as e:
            print(f"保存消息数据失败: {e}")
            return False
    
    def send_message(self, sender: str, recipient: str, title: str, content: str, 
                    message_type: str = 'user') -> Tuple[bool, str]:
        """发送站内信
        
        Args:
            sender: 发送者用户名
            recipient: 接收者用户名 ('all' 表示发送给所有用户)
            title: 消息标题
            content: 消息内容
            message_type: 消息类型 ('user', 'admin', 'system')
        
        Returns:
            (success, message_id or error_message)
        """
        try:
            if not title.strip() or not content.strip():
                return False, "标题和内容不能为空"
            
            message_id = str(uuid.uuid4())
            timestamp = beijing_now_iso()
            
            message = {
                'id': message_id,
                'sender': sender,
                'recipient': recipient,
                'title': title.strip(),
                'content': content.strip(),
                'type': message_type,
                'timestamp': timestamp,
                'is_read': False,
                'read_at': None
            }
            
            self.messages_data['messages'].append(message)
            
            # 更新统计信息
            self.messages_data['statistics']['total_messages'] += 1
            self.messages_data['statistics']['total_unread_messages'] += 1
            
            if message_type == 'system':
                self.messages_data['statistics']['total_system_messages'] += 1
            elif message_type == 'admin':
                self.messages_data['statistics']['total_admin_messages'] += 1
            
            if self.save_messages_data():
                return True, message_id
            else:
                return False, "保存消息失败"
                
        except Exception as e:
            print(f"发送消息失败: {e}")
            return False, f"发送失败: {str(e)}"
    
    def get_user_messages(self, username: str, limit: int = 50, 
                         offset: int = 0, unread_only: bool = False) -> List[Dict]:
        """获取用户的消息列表
        
        Args:
            username: 用户名
            limit: 返回消息数量限制
            offset: 偏移量
            unread_only: 是否只返回未读消息
        
        Returns:
            消息列表
        """
        try:
            # 筛选属于该用户的消息（包括发给所有人的消息）
            user_messages = []
            for message in self.messages_data['messages']:
                if message['recipient'] == username or message['recipient'] == 'all':
                    if not unread_only or not message['is_read']:
                        user_messages.append(message.copy())
            
            # 按时间倒序排列
            user_messages.sort(key=lambda x: x['timestamp'], reverse=True)
            
            # 应用分页
            return user_messages[offset:offset + limit]
            
        except Exception as e:
            print(f"获取用户消息失败: {e}")
            return []
    
    def get_unread_count(self, username: str) -> int:
        """获取用户未读消息数量"""
        try:
            count = 0
            for message in self.messages_data['messages']:
                if (message['recipient'] == username or message['recipient'] == 'all') and not message['is_read']:
                    count += 1
            return count
        except Exception as e:
            print(f"获取未读消息数量失败: {e}")
            return 0
    
    def mark_as_read(self, message_id: str, username: str) -> Tuple[bool, str]:
        """标记消息为已读
        
        Args:
            message_id: 消息ID
            username: 用户名
        
        Returns:
            (success, message)
        """
        try:
            for message in self.messages_data['messages']:
                if (message['id'] == message_id and 
                    (message['recipient'] == username or message['recipient'] == 'all')):
                    
                    if not message['is_read']:
                        message['is_read'] = True
                        message['read_at'] = beijing_now_iso()
                        
                        # 更新统计信息
                        self.messages_data['statistics']['total_read_messages'] += 1
                        self.messages_data['statistics']['total_unread_messages'] -= 1
                        
                        if self.save_messages_data():
                            return True, "消息已标记为已读"
                        else:
                            return False, "保存失败"
                    else:
                        return True, "消息已经是已读状态"
            
            return False, "消息不存在或无权限"
            
        except Exception as e:
            print(f"标记消息已读失败: {e}")
            return False, f"操作失败: {str(e)}"
    
    def mark_all_as_read(self, username: str) -> Tuple[bool, str]:
        """标记用户所有消息为已读"""
        try:
            marked_count = 0
            timestamp = beijing_now_iso()
            
            for message in self.messages_data['messages']:
                if ((message['recipient'] == username or message['recipient'] == 'all') 
                    and not message['is_read']):
                    message['is_read'] = True
                    message['read_at'] = timestamp
                    marked_count += 1
            
            if marked_count > 0:
                # 更新统计信息
                self.messages_data['statistics']['total_read_messages'] += marked_count
                self.messages_data['statistics']['total_unread_messages'] -= marked_count
                
                if self.save_messages_data():
                    return True, f"已标记 {marked_count} 条消息为已读"
                else:
                    return False, "保存失败"
            else:
                return True, "没有未读消息"
                
        except Exception as e:
            print(f"批量标记已读失败: {e}")
            return False, f"操作失败: {str(e)}"
    
    def delete_message(self, message_id: str, username: str) -> Tuple[bool, str]:
        """删除消息（仅限用户删除自己收到的消息）"""
        try:
            for i, message in enumerate(self.messages_data['messages']):
                if (message['id'] == message_id and 
                    (message['recipient'] == username or message['recipient'] == 'all')):
                    
                    # 如果是未读消息，更新统计
                    if not message['is_read']:
                        self.messages_data['statistics']['total_unread_messages'] -= 1
                    else:
                        self.messages_data['statistics']['total_read_messages'] -= 1
                    
                    self.messages_data['statistics']['total_messages'] -= 1
                    
                    # 删除消息
                    del self.messages_data['messages'][i]
                    
                    if self.save_messages_data():
                        return True, "消息已删除"
                    else:
                        return False, "保存失败"
            
            return False, "消息不存在或无权限"
            
        except Exception as e:
            print(f"删除消息失败: {e}")
            return False, f"删除失败: {str(e)}"
    
    def get_message_by_id(self, message_id: str) -> Optional[Dict]:
        """根据ID获取消息"""
        try:
            for message in self.messages_data['messages']:
                if message['id'] == message_id:
                    return message.copy()
            return None
        except Exception as e:
            print(f"获取消息失败: {e}")
            return None
    
    def get_statistics(self) -> Dict:
        """获取消息统计信息"""
        return self.messages_data['statistics'].copy()
    
    def cleanup_old_messages(self, days_to_keep: int = 30) -> Tuple[bool, str]:
        """清理旧消息"""
        try:
            from datetime import datetime, timedelta
            cutoff_date = datetime.now() - timedelta(days=days_to_keep)
            cutoff_iso = cutoff_date.isoformat()
            
            original_count = len(self.messages_data['messages'])
            
            # 保留较新的消息
            self.messages_data['messages'] = [
                msg for msg in self.messages_data['messages']
                if msg['timestamp'] > cutoff_iso
            ]
            
            deleted_count = original_count - len(self.messages_data['messages'])
            
            if deleted_count > 0:
                # 重新计算统计信息
                self.recalculate_statistics()
                
                if self.save_messages_data():
                    return True, f"已清理 {deleted_count} 条旧消息"
                else:
                    return False, "保存失败"
            else:
                return True, "没有需要清理的消息"
                
        except Exception as e:
            print(f"清理旧消息失败: {e}")
            return False, f"清理失败: {str(e)}"
    
    def recalculate_statistics(self):
        """重新计算统计信息"""
        stats = {
            'total_messages': len(self.messages_data['messages']),
            'total_read_messages': 0,
            'total_unread_messages': 0,
            'total_system_messages': 0,
            'total_admin_messages': 0
        }
        
        for message in self.messages_data['messages']:
            if message['is_read']:
                stats['total_read_messages'] += 1
            else:
                stats['total_unread_messages'] += 1
            
            if message['type'] == 'system':
                stats['total_system_messages'] += 1
            elif message['type'] == 'admin':
                stats['total_admin_messages'] += 1
        
        self.messages_data['statistics'] = stats
