#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
站内信系统演示脚本
"""

from message_system import MessageSystem
from auth import UserManager
import time

def demo_message_system():
    """演示站内信系统功能"""
    print("🎉 站内信系统演示")
    print("=" * 50)
    
    message_system = MessageSystem()
    user_manager = UserManager()
    
    # 获取用户列表
    users = list(user_manager.users.keys())
    print(f"📋 系统中的用户: {', '.join(users[:5])}{'...' if len(users) > 5 else ''}")
    print(f"👥 总用户数: {len(users)}")
    print()
    
    # 演示发送不同类型的消息
    demo_messages = [
        {
            'title': '🎊 欢迎使用站内信系统',
            'content': '亲爱的用户，欢迎使用我们全新的站内信功能！您可以在这里接收重要通知、系统公告等信息。',
            'type': 'system',
            'recipient': 'all'
        },
        {
            'title': '🔧 系统维护通知',
            'content': '系统将在今晚 23:00-01:00 进行例行维护，期间可能会影响服务使用。维护内容包括：\n1. 数据库优化\n2. 安全更新\n3. 性能提升\n\n感谢您的理解与支持！',
            'type': 'admin',
            'recipient': 'all'
        },
        {
            'title': '🆕 新功能上线公告',
            'content': '我们很高兴地宣布以下新功能已经上线：\n• 站内信系统\n• 消息通知提醒\n• 批量消息管理\n\n快去体验吧！',
            'type': 'admin',
            'recipient': 'all'
        }
    ]
    
    print("📤 正在发送演示消息...")
    for i, msg in enumerate(demo_messages, 1):
        success, result = message_system.send_message(
            sender='admin',
            recipient=msg['recipient'],
            title=msg['title'],
            content=msg['content'],
            message_type=msg['type']
        )
        
        status = "✅ 成功" if success else "❌ 失败"
        print(f"  {i}. {msg['title']} - {status}")
        time.sleep(0.5)  # 模拟发送间隔
    
    print()
    
    # 发送个人消息给管理员
    if 'admin' in users:
        print("📨 发送管理员专用消息...")
        success, result = message_system.send_message(
            sender='system',
            recipient='admin',
            title='🛡️ 管理员专用 - 系统状态报告',
            content='站内信系统已成功部署并运行正常！\n\n系统状态：\n• 消息发送功能：正常\n• 数据存储：正常\n• 用户界面：正常\n\n所有功能测试通过，可以正式使用。',
            message_type='system'
        )
        status = "✅ 成功" if success else "❌ 失败"
        print(f"  管理员专用消息 - {status}")
        print()
    
    # 显示统计信息
    stats = message_system.get_statistics()
    print("📊 当前系统统计:")
    print(f"  📧 总消息数: {stats['total_messages']}")
    print(f"  📬 未读消息: {stats['total_unread_messages']}")
    print(f"  📭 已读消息: {stats['total_read_messages']}")
    print(f"  👨‍💼 管理员消息: {stats['total_admin_messages']}")
    print(f"  🤖 系统消息: {stats['total_system_messages']}")
    print()
    
    # 演示用户消息查看
    if users:
        demo_user = users[0]
        print(f"👤 演示用户 '{demo_user}' 的消息:")
        user_messages = message_system.get_user_messages(demo_user, limit=5)
        unread_count = message_system.get_unread_count(demo_user)
        
        print(f"  📨 收到消息: {len(user_messages)} 条")
        print(f"  🔴 未读消息: {unread_count} 条")
        
        if user_messages:
            print("  📋 最新消息:")
            for msg in user_messages[:3]:
                read_status = "📭" if msg['is_read'] else "📬"
                msg_type = {"admin": "👨‍💼", "system": "🤖", "user": "👤"}.get(msg['type'], "📄")
                print(f"    {read_status} {msg_type} {msg['title']}")
    
    print()
    print("🎯 功能演示完成！")
    print("💡 提示：")
    print("  1. 登录系统后，在主页右上角可以看到站内信按钮")
    print("  2. 如有未读消息，会显示红色数字提醒")
    print("  3. 管理员可以在管理面板中发送站内信")
    print("  4. 用户可以在站内信页面查看和管理消息")
    print()
    print("🌐 现在可以打开浏览器访问 http://localhost:7799 体验完整功能！")

if __name__ == "__main__":
    demo_message_system()
