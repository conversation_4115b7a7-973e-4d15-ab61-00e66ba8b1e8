# 站内信系统使用说明

## 功能概述

站内信系统为梦羽AI绘图工具添加了完整的内部消息功能，支持管理员向用户发送通知、公告等消息，用户可以方便地查看和管理收到的消息。

## 主要功能

### 管理员功能

1. **发送站内信**
   - 可以发送给指定用户
   - 可以发送给所有用户（群发）
   - 支持不同消息类型（管理员消息、系统消息）

2. **消息统计**
   - 查看总消息数、已读/未读消息统计
   - 查看不同类型消息的数量

### 用户功能

1. **查看消息**
   - 在主页显示未读消息数量提醒
   - 专门的站内信页面查看所有消息
   - 支持筛选未读消息

2. **消息管理**
   - 标记单条消息为已读
   - 批量标记所有消息为已读
   - 删除不需要的消息

## 使用方法

### 管理员使用

1. **登录管理员账户**
   - 使用管理员账户登录系统
   - 点击"管理面板"进入后台

2. **发送站内信**
   - 在管理面板中点击"站内信管理"选项卡
   - 填写收件人（用户名或"all"表示所有用户）
   - 输入消息标题和内容
   - 点击"发送消息"

3. **查看统计**
   - 在站内信管理页面可以看到消息统计信息
   - 点击"刷新统计"获取最新数据

### 用户使用

1. **查看未读消息提醒**
   - 登录后在主页右上角可以看到站内信按钮
   - 如有未读消息，会显示红色数字徽章

2. **进入站内信页面**
   - 点击主页的"站内信"按钮
   - 或直接访问 `/messages` 页面

3. **管理消息**
   - 查看所有消息或只查看未读消息
   - 点击"查看"按钮查看消息详情
   - 点击"标记已读"将消息标记为已读
   - 点击"删除"删除不需要的消息
   - 使用"全部标记已读"批量处理

## 技术实现

### 后端组件

1. **MessageSystem 类** (`message_system.py`)
   - 消息数据管理
   - 消息发送、接收、标记已读等核心功能
   - 统计信息计算

2. **API 路由** (`app.py`)
   - `/api/messages` - 获取消息列表
   - `/api/messages/unread_count` - 获取未读数量
   - `/api/messages/<id>/read` - 标记已读
   - `/api/messages/read_all` - 批量标记已读
   - `/api/messages/<id>` - 获取/删除消息
   - `/admin/messages/send` - 管理员发送消息
   - `/admin/messages/statistics` - 获取统计信息

### 前端组件

1. **主页集成** (`index.html`)
   - 站内信入口按钮
   - 未读消息数量徽章
   - 自动刷新未读数量

2. **管理员后台** (`admin.html`)
   - 站内信管理选项卡
   - 发送消息表单
   - 统计信息显示

3. **站内信页面** (`messages.html`)
   - 消息列表显示
   - 消息筛选功能
   - 消息详情查看
   - 消息管理操作

### 数据存储

消息数据存储在 `messages.json` 文件中，包含：
- 消息列表（ID、发送者、接收者、标题、内容、类型、时间戳、已读状态等）
- 统计信息（总消息数、已读/未读数量、不同类型消息数量）

## 消息类型

1. **用户消息** (`user`) - 普通用户之间的消息
2. **管理员消息** (`admin`) - 管理员发送的消息
3. **系统消息** (`system`) - 系统自动发送的消息

## 安全考虑

1. **权限控制**
   - 只有管理员可以发送站内信
   - 用户只能查看发给自己或发给所有人的消息
   - 用户只能删除自己收到的消息

2. **数据验证**
   - 消息标题和内容不能为空
   - 收件人必须存在（除了"all"）
   - 消息长度限制

## 扩展功能建议

1. **消息回复** - 支持用户回复消息
2. **消息分类** - 添加更多消息分类
3. **消息搜索** - 支持按关键词搜索消息
4. **消息导出** - 支持导出消息记录
5. **邮件通知** - 重要消息可发送邮件提醒
6. **消息模板** - 管理员可使用预设模板
7. **定时发送** - 支持定时发送消息

## 故障排除

1. **消息发送失败**
   - 检查收件人是否存在
   - 确认消息内容不为空
   - 查看服务器日志

2. **未读数量不更新**
   - 刷新页面
   - 检查网络连接
   - 查看浏览器控制台错误

3. **页面加载失败**
   - 确认用户已登录
   - 检查服务器运行状态
   - 查看浏览器网络请求

## 维护建议

1. **定期清理** - 可以定期清理过期消息
2. **备份数据** - 定期备份 `messages.json` 文件
3. **监控统计** - 关注消息发送量和用户活跃度
4. **性能优化** - 大量消息时考虑分页和索引优化
