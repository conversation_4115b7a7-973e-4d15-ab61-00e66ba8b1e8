#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建测试消息的脚本
"""

from message_system import MessageSystem
from auth import UserManager

def create_test_messages():
    """创建一些测试消息"""
    print("=== 创建测试消息 ===")
    
    message_system = MessageSystem()
    user_manager = UserManager()
    
    # 获取现有用户
    users = list(user_manager.users.keys())
    print(f"现有用户: {users}")
    
    if not users:
        print("没有找到用户，请先创建用户")
        return
    
    # 为每个用户创建一些测试消息
    test_messages = [
        {
            'title': '欢迎使用站内信系统',
            'content': '欢迎使用我们全新的站内信功能！您可以在这里接收系统通知、管理员消息等。',
            'type': 'system'
        },
        {
            'title': '系统维护通知',
            'content': '系统将在今晚 23:00-01:00 进行例行维护，期间可能会影响服务使用，请您提前做好准备。',
            'type': 'admin'
        },
        {
            'title': '新功能上线',
            'content': '我们刚刚上线了站内信功能，您现在可以接收重要通知了。如有任何问题，请联系管理员。',
            'type': 'admin'
        }
    ]
    
    # 发送给所有用户的消息
    for msg in test_messages:
        success, result = message_system.send_message(
            sender='admin',
            recipient='all',
            title=msg['title'],
            content=msg['content'],
            message_type=msg['type']
        )
        print(f"发送消息 '{msg['title']}': {success}")
    
    # 发送给特定用户的消息
    if 'admin' in users:
        success, result = message_system.send_message(
            sender='system',
            recipient='admin',
            title='管理员专用消息',
            content='这是一条只有管理员才能看到的消息。站内信系统已经成功部署！',
            message_type='system'
        )
        print(f"发送管理员专用消息: {success}")
    
    # 显示统计信息
    stats = message_system.get_statistics()
    print(f"\n当前统计信息:")
    print(f"  总消息数: {stats['total_messages']}")
    print(f"  未读消息: {stats['total_unread_messages']}")
    print(f"  已读消息: {stats['total_read_messages']}")
    print(f"  管理员消息: {stats['total_admin_messages']}")
    print(f"  系统消息: {stats['total_system_messages']}")
    
    print("\n=== 测试消息创建完成 ===")
    print("现在您可以登录系统查看站内信功能了！")

if __name__ == "__main__":
    create_test_messages()
